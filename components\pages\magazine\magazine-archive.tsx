import MagazineItem from "./magazine-item";

export default function MagazineArchive() {
   const currentYear = new Date().getFullYear();
   
   // Generate the 3 most recent years of magazines
   const magazines = [
      {
         year: currentYear - 1, // 2023
         coverImage: "/images/hero/hero-bg.png",
         description: "A year of groundbreaking creativity and innovation. Featuring visionaries who redefined their industries and inspired a new generation of creators."
      },
      {
         year: currentYear - 2, // 2022
         coverImage: "/images/hero/hero-bg.png", 
         description: "Celebrating resilience and adaptation. Showcasing creatives who transformed challenges into opportunities and emerged stronger than ever."
      },
      {
         year: currentYear - 3, // 2021
         coverImage: "/images/hero/hero-bg.png",
         description: "The year of digital transformation. Highlighting pioneers who embraced new technologies and platforms to reach global audiences."
      }
   ];

   return (
      <section className="mx-auto max-w-[1380px] px-4 py-16 md:px-8">
         {/* Section Header */}
         <div className="mb-12 text-center">
            <h2 className="text-3xl font-bold md:text-4xl">Magazine Archive</h2>
            <p className="mt-4 text-lg text-gray-600">
               Explore our collection of annual magazines celebrating exceptional creativity
            </p>
         </div>

         {/* Magazine Grid */}
         <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
            {magazines.map((magazine) => (
               <MagazineItem
                  key={magazine.year}
                  year={magazine.year}
                  coverImage={magazine.coverImage}
                  description={magazine.description}
               />
            ))}
         </div>

         {/* Additional Info */}
         <div className="mt-16 text-center">
            <p className="text-gray-600">
               Looking for older editions? Contact us at{" "}
               <a 
                  href="mailto:<EMAIL>" 
                  className="text-black underline hover:no-underline"
                  aria-label="Email us for older magazine editions"
               >
                  <EMAIL>
               </a>
            </p>
         </div>
      </section>
   );
}
