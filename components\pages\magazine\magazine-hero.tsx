import { Button } from "@/components/ui/button";
import Image from "next/image";

export default function MagazineHero() {
   const currentYear = new Date().getFullYear();
   const previousYear = currentYear - 1;

   return (
      <section className="relative h-screen overflow-hidden">
         {/* Background Image */}
         <div className="absolute inset-0 z-0">
            <Image
               src="/images/hero/hero-bg.png"
               alt="Magazine preview background"
               fill
               priority
               className="object-cover grayscale"
            />
         </div>

         {/* Magazine Preview */}
         <div className="relative z-10 mx-auto flex h-full max-w-[1380px] items-center justify-center px-4 md:px-8">
            <div className="text-center">
               {/* Magazine Cover Preview */}
               <div className="relative mx-auto mb-8 aspect-[3/4] w-[300px] overflow-hidden rounded-lg shadow-2xl md:w-[400px]">
                  <Image
                     src="/images/hero/hero-bg.png"
                     alt={`Mlist Magazine ${previousYear}`}
                     fill
                     className="object-cover"
                  />
                  {/* Magazine Title Overlay */}
                  <div className="absolute inset-0 bg-black/40"></div>
                  <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
                     <h2 className="text-4xl font-bold md:text-6xl">MLIST</h2>
                     <p className="mt-2 text-lg md:text-xl">MAGAZINE</p>
                     <p className="mt-4 text-2xl font-light md:text-3xl">{previousYear}</p>
                  </div>
               </div>

               {/* Hero Text */}
               <div className="mb-8">
                  <h1 className="text-4xl font-bold text-white md:text-6xl">
                     Annual Collection
                  </h1>
                  <p className="mt-4 text-lg text-white/90 md:text-xl">
                     Featuring the year&apos;s most exceptional creatives
                  </p>
               </div>

               {/* Download Button */}
               <Button
                  className="rounded-2xl border-2 border-gray-400 bg-black px-8 py-6 text-lg text-gray-200 shadow-md shadow-black hover:bg-gray-950"
                  variant="secondary"
                  aria-label={`Download Mlist Magazine ${previousYear}`}
               >
                  Download {previousYear} Edition
               </Button>
            </div>
         </div>
      </section>
   );
}
