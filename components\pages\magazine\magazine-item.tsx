import { Button } from "@/components/ui/button";
import Image from "next/image";

interface MagazineItemProps {
   year: number;
   coverImage: string;
   description: string;
}

export default function MagazineItem({ year, coverImage, description }: MagazineItemProps) {
   return (
      <article className="group overflow-hidden rounded-lg bg-white shadow-lg transition-all duration-300 hover:shadow-xl">
         {/* Magazine Cover */}
         <div className="relative aspect-[3/4] overflow-hidden">
            <Image
               src={coverImage}
               alt={`Mlist Magazine ${year} cover`}
               fill
               className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            {/* Cover Overlay */}
            <div className="absolute inset-0 bg-black/30 transition-opacity duration-300 group-hover:bg-black/20"></div>
            
            {/* Magazine Title on Cover */}
            <div className="absolute inset-0 flex flex-col items-center justify-center text-white">
               <h3 className="text-3xl font-bold md:text-4xl">MLIST</h3>
               <p className="mt-1 text-sm md:text-base">MAGAZINE</p>
               <p className="mt-3 text-xl font-light md:text-2xl">{year}</p>
            </div>
         </div>

         {/* Content */}
         <div className="p-6">
            <div className="mb-4">
               <h2 className="text-xl font-bold md:text-2xl">
                  {year} Collection
               </h2>
               <p className="mt-2 text-gray-600">
                  {description}
               </p>
            </div>

            {/* Download Button */}
            <Button
               className="w-full rounded-xl border border-gray-300 bg-white px-6 py-3 text-black transition-all duration-300 hover:bg-gray-50 hover:border-gray-400"
               variant="outline"
               aria-label={`Download Mlist Magazine ${year}`}
            >
               <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2"
               >
                  <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                  <polyline points="7,10 12,15 17,10" />
                  <line x1="12" x2="12" y1="15" y2="3" />
               </svg>
               Download PDF
            </Button>
         </div>
      </article>
   );
}
