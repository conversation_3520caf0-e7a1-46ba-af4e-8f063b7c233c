import ActiveLink from "@/components/ui/active-link";
import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function Navigation() {
   return (
      <header className="fixed top-0 right-0 left-0 z-90 h-[100px] bg-black px-2 text-white">
         <div className="mx-auto max-w-[1380px] px-4 md:px-8">
            <div className="flex h-[100px] items-center justify-between">
               <Link href="/" className="flex items-center">
                  <div className="relative mr-2 h-18 w-18">
                     <Image
                        src="/images/logo.png"
                        alt="Mlist Logo"
                        fill
                        className="object-contain"
                     />
                  </div>
               </Link>
               <nav className="hidden items-center space-x-8 md:flex">
                  <ActiveLink href="/" exactMatch>
                     Home
                  </ActiveLink>
                  <ActiveLink href="/the-list">The List</ActiveLink>
                  <ActiveLink href="/profiles">Profiles</ActiveLink>
                  <ActiveLink href="/gallery">Gallery</ActiveLink>
                  <ActiveLink href="/magazine">Magazine</ActiveLink>
                  <ActiveLink href="/newsletter">Newsletter</ActiveLink>
                  <Button
                     asChild
                     className="mx-0 rounded-2xl border-2 border-gray-400 bg-black px-7 py-5 text-base text-gray-300 shadow-md shadow-black hover:bg-gray-950 md:mx-4 lg:mx-6"
                     variant="secondary"
                  >
                     <Link
                        href="https://docs.google.com/forms/d/e/1FAIpQLSew1e_uhrpuLbtEVDM3gwjPzqiaDmW8KNQRgKy1BIJvCq97JA/viewform?usp=dialog"
                        target="_blank"
                     >
                        Nominate
                     </Link>
                  </Button>
               </nav>
               <div className="md:hidden">
                  {/* Mobile menu button would go here */}
                  <button className="p-2">
                     <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="lucide lucide-menu"
                     >
                        <line x1="4" x2="20" y1="12" y2="12"></line>
                        <line x1="4" x2="20" y1="6" y2="6"></line>
                        <line x1="4" x2="20" y1="18" y2="18"></line>
                     </svg>
                  </button>
               </div>
            </div>
         </div>
      </header>
   );
}
